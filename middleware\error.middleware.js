const {
    ValidationError,
    DatabaseError,
    ConnectionError,
    ConnectionAcquireTimeoutError,
    ConnectionRefusedError,
    ConnectionTimedOutError,
    HostNotFoundError,
    HostNotReachableError,
    InvalidConnectionError,
    TimeoutError
} = require("sequelize");
const { ApiError } = require("../helpers/api.helper");
const logger = require("../config/logger");

// Error Response Function
const sendError = (res, message, code = 400, data = []) => {
    res.status(code).send({
        status: false,
        message,
        data,
    });
};

const errorConverter = (err, req, res, next) => {
    let error = err;
    if (!(error instanceof ApiError)) {
        let statusCode = 500;
        let message = "Internal Server Error";

        if (error instanceof ValidationError) {
            statusCode = 400;
            message = error.message;
        } else if (error instanceof DatabaseError) {
            statusCode = 500;
            message = error.message;
        } else if (error instanceof ConnectionError ||
                   error instanceof ConnectionAcquireTimeoutError ||
                   error instanceof ConnectionRefusedError ||
                   error instanceof ConnectionTimedOutError ||
                   error instanceof HostNotFoundError ||
                   error instanceof HostNotReachableError ||
                   error instanceof InvalidConnectionError ||
                   error instanceof TimeoutError ||
                   error.name === 'SequelizeConnectionAcquireTimeoutError' ||
                   error.name === 'ConnectionAcquireTimeoutError' ||
                   error.name === 'TimeoutError') {
            statusCode = 503; // Service Unavailable
            message = "Database service temporarily unavailable. Please try again later.";
        } else if (error.statusCode) {
            statusCode = error.statusCode;
            message = error.message || "Internal Server Error";
        }

        error = new ApiError(statusCode, message, false, err.stack);
    }
    next(error);
};

// eslint-disable-next-line no-unused-vars
const errorHandler = (err, req, res, next) => {
    let { statusCode, message } = err;
    const isProduction = process.env.NODE_ENV === "production";

    if (isProduction && !err.isOperational) {
        statusCode = 500;
        message = "Internal Server Error";
    }

    res.locals.errorMessage = err.message;

    // Log the error
    logger.error(`${req.method} ${req.url} - ${statusCode} - ${message}`, {
        error: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });

    let data = [];
    if (!isProduction) {
        data = err.stack;
    }

    return sendError(res, message, statusCode, data);
};

module.exports = {
    errorConverter,
    errorHandler,
};
