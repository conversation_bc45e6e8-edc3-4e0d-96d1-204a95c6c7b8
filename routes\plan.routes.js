const express = require('express');
const router = express.Router();
const { getAllActivePlans } = require('../controllers/plan.controller');
const { verifyToken } = require('../middleware/auth.middleware');
const asyncHandler = require('../helpers/asyncHandler');

// User routes for viewing active plans (requires authentication)
router.get('/', verifyToken, asyncHandler(getAllActivePlans));

module.exports = router;
