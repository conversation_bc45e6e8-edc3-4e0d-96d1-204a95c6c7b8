const jwt = require('jsonwebtoken');
const logger = require('../config/logger');

exports.verifyToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];

    // Check for Bearer token
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger.warn(`Missing or malformed authorization header from ${req.ip} for ${req.method} ${req.url}`);
        return res.status(403).json({
            status: false,
            message: 'Authorization token is missing or malformed',
            data: []
        });
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

        // Check if user has user role
        if (decoded.role !== 'user') {
            logger.warn(`Access denied for user ${decoded.user_name || decoded.id} - User role required for ${req.method} ${req.url}`);
            return res.status(403).json({
                status: false,
                message: 'Access denied. User role required.',
                data: []
            });
        }

        next();
    } catch (err) {
        // Log different types of JWT errors with appropriate levels
        if (err.name === 'TokenExpiredError') {
            logger.warn(`JWT token expired for ${req.method} ${req.url} from ${req.ip}`, {
                expiredAt: err.expiredAt,
                url: req.url,
                method: req.method,
                ip: req.ip
            });
            return res.status(401).json({
                status: false,
                message: 'Token has expired. Please login again.',
                data: [],
                code: 'TOKEN_EXPIRED'
            });
        } else if (err.name === 'JsonWebTokenError') {
            logger.warn(`Invalid JWT token for ${req.method} ${req.url} from ${req.ip}`, {
                error: err.message,
                url: req.url,
                method: req.method,
                ip: req.ip
            });
            return res.status(401).json({
                status: false,
                message: 'Invalid token. Please login again.',
                data: [],
                code: 'INVALID_TOKEN'
            });
        } else {
            logger.error(`JWT verification error for ${req.method} ${req.url} from ${req.ip}`, {
                error: err.message,
                stack: err.stack,
                url: req.url,
                method: req.method,
                ip: req.ip
            });
            return res.status(401).json({
                status: false,
                message: 'Token verification failed. Please login again.',
                data: [],
                code: 'TOKEN_VERIFICATION_FAILED'
            });
        }
    }
};

exports.verifyAdmin = (req, res, next) => {
    // First verify the token
    const authHeader = req.headers['authorization'];

    // Check for Bearer token
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger.warn(`Missing or malformed authorization header from ${req.ip} for admin access ${req.method} ${req.url}`);
        return res.status(403).json({
            status: false,
            message: 'Authorization token is missing or malformed',
            data: []
        });
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

        // Check if user has admin role
        if (decoded.role !== 'admin') {
            logger.warn(`Access denied for user ${decoded.user_name || decoded.id} - Admin role required for ${req.method} ${req.url}`);
            return res.status(403).json({
                status: false,
                message: 'Access denied. Admin role required.',
                data: []
            });
        }

        next();
    } catch (err) {
        // Log different types of JWT errors with appropriate levels
        if (err.name === 'TokenExpiredError') {
            logger.warn(`Admin JWT token expired for ${req.method} ${req.url} from ${req.ip}`, {
                expiredAt: err.expiredAt,
                url: req.url,
                method: req.method,
                ip: req.ip
            });
            return res.status(401).json({
                status: false,
                message: 'Token has expired. Please login again.',
                data: [],
                code: 'TOKEN_EXPIRED'
            });
        } else if (err.name === 'JsonWebTokenError') {
            logger.warn(`Invalid admin JWT token for ${req.method} ${req.url} from ${req.ip}`, {
                error: err.message,
                url: req.url,
                method: req.method,
                ip: req.ip
            });
            return res.status(401).json({
                status: false,
                message: 'Invalid token. Please login again.',
                data: [],
                code: 'INVALID_TOKEN'
            });
        } else {
            logger.error(`Admin JWT verification error for ${req.method} ${req.url} from ${req.ip}`, {
                error: err.message,
                stack: err.stack,
                url: req.url,
                method: req.method,
                ip: req.ip
            });
            return res.status(401).json({
                status: false,
                message: 'Token verification failed. Please login again.',
                data: [],
                code: 'TOKEN_VERIFICATION_FAILED'
            });
        }
    }
};
