const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const db = require('./models');
const routes = require('./routes');
const { errorConverter, errorHandler } = require("./middleware/error.middleware");
const logger = require('./config/logger');

// 📦 Load environment variables from .env
dotenv.config();

// 🚀 Initialize app
const app = express();

// 🛡️ Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add request logging middleware
app.use((req, res, next) => {
    logger.http(`${req.method} ${req.url} - ${req.ip}`);
    next();
});

// 🔗 Routes
app.use('/', routes);

// Error handling middleware (should be after routes)
app.use(errorConverter);
app.use(errorHandler);

// ⚙️ Connect to DB and start server
const PORT = process.env.PORT || 3000;



// Graceful shutdown handling
let server;

const gracefulShutdown = (signal) => {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);

    if (server) {
        server.close(() => {
            logger.info('HTTP server closed.');

            // Close database connections
            db.sequelize.close()
                .then(() => {
                    logger.info('Database connections closed.');
                    process.exit(0);
                })
                .catch((err) => {
                    logger.error('Error closing database connections:', err);
                    process.exit(1);
                });
        });

        // Force close after 30 seconds
        setTimeout(() => {
            logger.error('Could not close connections in time, forcefully shutting down');
            process.exit(1);
        }, 30000);
    } else {
        process.exit(0);
    }
};

// Handle different termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
    });

    // Don't exit immediately, try graceful shutdown first
    gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection:', {
        reason: reason,
        promise: promise,
        timestamp: new Date().toISOString()
    });

    // Don't exit immediately, try graceful shutdown first
    gracefulShutdown('UNHANDLED_REJECTION');
});

// Database connection with retry logic
const connectWithRetry = async (retries = 5, delay = 5000) => {
    for (let i = 0; i < retries; i++) {
        try {
            logger.info(`Attempting database connection... (${i + 1}/${retries})`);

            // Test database connection
            await db.sequelize.authenticate();
            logger.info('✅ Database connection established successfully');

            // Use force: false and alter: false to avoid index conflicts
            // For production, use migrations instead of sync
            await db.sequelize.sync({ force: false, alter: false });
            logger.info('✅ Database synchronized');

            // Start server
            server = app.listen(PORT, () => {
                logger.info(`🚀 Server running on http://localhost:${PORT}`);
                logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
                logger.info(`🔗 Database: ${process.env.DB_NAME || 'gold'} on ${process.env.DB_HOST || '127.0.0.1'}`);
            });

            // Handle server errors
            server.on('error', (error) => {
                if (error.syscall !== 'listen') {
                    throw error;
                }

                const bind = typeof PORT === 'string' ? 'Pipe ' + PORT : 'Port ' + PORT;

                switch (error.code) {
                    case 'EACCES':
                        logger.error(`${bind} requires elevated privileges`);
                        process.exit(1);
                        break;
                    case 'EADDRINUSE':
                        logger.error(`${bind} is already in use`);
                        process.exit(1);
                        break;
                    default:
                        throw error;
                }
            });

            return; // Success, exit retry loop

        } catch (err) {
            logger.error(`❌ Database connection attempt ${i + 1} failed:`, {
                error: err.message,
                code: err.code,
                errno: err.errno,
                syscall: err.syscall,
                hostname: err.hostname
            });

            if (i === retries - 1) {
                logger.error('❌ All database connection attempts failed. Exiting...');
                process.exit(1);
            }

            logger.info(`⏳ Retrying in ${delay / 1000} seconds...`);
            await new Promise(resolve => setTimeout(resolve, delay));

            // Increase delay for next retry (exponential backoff)
            delay = Math.min(delay * 1.5, 30000);
        }
    }
};

// Start the application
connectWithRetry();
