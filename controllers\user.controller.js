const { User, Referral } = require('../models');
const BaseController = require('../helpers/BaseController');
const { Op } = require('sequelize');
const logger = require('../config/logger');

exports.getProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await User.findByPk(userId, {
      attributes: ['user_id', 'user_name', 'name', 'email', 'mobile', 'referral_code', 'createdAt']
    });

    if (!user) {
      return BaseController.sendError(res, 'User not found');
    }

    return BaseController.sendResponse(res, user, 'User profile fetched successfully');
  } catch (err) {
    logger.error('Get profile error:', err);
    return BaseController.sendError(res, 'Server error', [{ error: err.message }]);
  }
};

exports.getReferralCount = async (req, res) => {
  try {
    const userId = req.user.id;

    // Check if user exists with timeout
    const user = await User.findByPk(userId, {
      attributes: ['user_id', 'user_name'],
      timeout: 30000 // 30 second timeout
    });

    if (!user) {
      logger.warn(`User not found for referral count: ${userId}`);
      return BaseController.sendError(res, 'User not found');
    }

    // Get level 1 count with timeout
    const level1Count = await Referral.count({
      where: {
        referrer_id: userId,
        level: 1,
        status: 'active'
      },
      timeout: 30000
    });

    // Get level 2 & 3 combined count with timeout
    const level2And3Count = await Referral.count({
      where: {
        referrer_id: userId,
        level: {
          [Op.in]: [2, 3]
        },
        status: 'active'
      },
      timeout: 30000
    });

    const referralData = {
      level_1: level1Count,
      level_2_and_3: level2And3Count,
      total: level1Count + level2And3Count
    };

    logger.info(`Referral count fetched for user ${userId}:`, referralData);
    return BaseController.sendResponse(res, referralData, 'Referral count fetched successfully');
  } catch (err) {
    logger.error('Get referral count error:', {
      error: err.message,
      stack: err.stack,
      userId: req.user?.id,
      url: req.url,
      method: req.method,
      ip: req.ip
    });

    // Handle specific database errors
    if (err.name === 'SequelizeConnectionAcquireTimeoutError') {
      return BaseController.sendError(res, 'Database connection timeout. Please try again later.', 503);
    } else if (err.name === 'SequelizeConnectionError') {
      return BaseController.sendError(res, 'Database connection error. Please try again later.', 503);
    } else if (err.name === 'SequelizeTimeoutError') {
      return BaseController.sendError(res, 'Operation timed out. Please try again later.', 503);
    }

    return BaseController.sendError(res, 'Server error', 500, [{ error: err.message }]);
  }
};
