const jwt = require('jsonwebtoken');
const logger = require('../config/logger');

exports.verifyToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];

    // Check for Bearer token
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(200).json({
            success: false,
            message: 'Authorization token is missing or malformed'
        });
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

        // Check if user has user role
        if (decoded.role !== 'user') {
            return res.status(200).json({
                success: false,
                message: 'Access denied. User role required.'
            });
        }

        next();
    } catch (err) {
        logger.error('JWT verification error:', {
            error: err.message,
            url: req.url,
            method: req.method,
            ip: req.ip
        });

        let message = 'Invalid or expired token';
        if (err.name === 'TokenExpiredError') {
            message = 'Token has expired. Please login again.';
        } else if (err.name === 'JsonWebTokenError') {
            message = 'Invalid token format.';
        }

        return res.status(200).json({
            success: false,
            message: message
        });
    }
};

exports.verifyAdmin = (req, res, next) => {
    // First verify the token
    const authHeader = req.headers['authorization'];

    // Check for Bearer token
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(200).json({
            success: false,
            message: 'Authorization token is missing or malformed'
        });
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

        // Check if user has admin role
        if (decoded.role !== 'admin') {
            return res.status(200).json({
                success: false,
                message: 'Access denied. Admin role required.'
            });
        }

        next();
    } catch (err) {
        logger.error('JWT verification error:', {
            error: err.message,
            url: req.url,
            method: req.method,
            ip: req.ip
        });

        let message = 'Invalid or expired token';
        if (err.name === 'TokenExpiredError') {
            message = 'Token has expired. Please login again.';
        } else if (err.name === 'JsonWebTokenError') {
            message = 'Invalid token format.';
        }

        return res.status(200).json({
            success: false,
            message: message
        });
    }
};
