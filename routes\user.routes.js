const express = require('express');
const router = express.Router();
const { getProfile, getReferralCount } = require('../controllers/user.controller');
const { verifyToken } = require('../middleware/auth.middleware');
const asyncHandler = require('../helpers/asyncHandler');

router.get('/profile', verifyToken, asyncHandler(getProfile));
router.get('/referral-count', verifyToken, asyncHandler(getReferralCount));

module.exports = router;
